[project]
name = "transcript"
version = "0.1.0"
description = "视频字幕处理工具 - 自动生成、编辑和处理视频字幕"
authors = [
    {name = "Flora-cj",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10,<3.13"
dependencies = [
    "jieba (>=0.42.1,<0.43.0)",
    "opencc (>=1.1.9,<2.0.0)",
    "pysubs2 (>=1.8.0,<2.0.0)",
    "pyyaml (>=6.0,<7.0)",
    "whisperx (>=3.3.4,<4.0.0)",
    "torch (>=2.0.0)",
    "torchvision (>=0.15.0)",
    "torchaudio (>=2.0.0)"
]

[project.scripts]
transcript = "transcript.cli:main"

[[tool.poetry.source]]
name = "ali"
url = "https://mirrors.aliyun.com/pypi/simple"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
